# Experience Studio Optimization Strategies

## Executive Summary

This document outlines comprehensive refactoring opportunities for the Experience Studio codebase, focusing on performance optimization and maintainability improvements. The strategies are prioritized by impact and implementation effort, with specific code examples and actionable steps.

## 🎯 High-Priority Refactoring Opportunities

### 1. Constructor Injection → inject() Migration
**Impact: HIGH | Effort: MEDIUM | Timeline: 2-3 weeks**

#### Current Issues
- Mixed usage of constructor injection and modern `inject()` function
- Inconsistent dependency injection patterns across components
- Harder to test and maintain

#### Affected Components
- `nav-header.component.ts` - 2 constructor injections
- `chat-window.component.ts` - 7+ constructor injections
- `toast.component.ts` - Multiple service injections
- `code-window.component.ts` - 15+ service dependencies

#### Implementation Strategy

**Before (Current Pattern):**
```typescript
export class ChatWindowComponent {
  constructor(
    private cdr: ChangeDetectorRef,
    private stepperStateService: StepperStateService,
    private contentSanitizationService: ContentSanitizationService,
    private promptService: PromptBarService,
    private toastService: ToastService,
    private regenerationIntegrationService: RegenerationIntegrationService,
    private sequentialRegenerationService: SequentialRegenerationService,
    private enhancedSSEService: EnhancedSSEService
  ) {
    this.destroyRef = inject(DestroyRef);
  }
}
```

**After (Recommended Pattern):**
```typescript
export class ChatWindowComponent {
  // Angular 19+ inject pattern
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly stepperStateService = inject(StepperStateService);
  private readonly contentSanitizationService = inject(ContentSanitizationService);
  private readonly promptService = inject(PromptBarService);
  private readonly toastService = inject(ToastService);
  private readonly regenerationIntegrationService = inject(RegenerationIntegrationService);
  private readonly sequentialRegenerationService = inject(SequentialRegenerationService);
  private readonly enhancedSSEService = inject(EnhancedSSEService);
  private readonly destroyRef = inject(DestroyRef);
}
```

#### Benefits
- Improved tree-shaking and bundle size optimization
- Better testability with easier mocking
- Consistent with Angular 19+ best practices
- Reduced constructor complexity

#### Implementation Steps
1. **Week 1**: Migrate 5 smallest components (nav-header, toast, theme-toggle)
2. **Week 2**: Migrate medium components (chat-window, code-viewer)
3. **Week 3**: Migrate large components (code-window, ui-design-code-preview)

### 2. BehaviorSubject → Signals Migration
**Impact: HIGH | Effort: HIGH | Timeline: 4-6 weeks**

#### Current Issues
- Heavy reliance on BehaviorSubjects for state management
- Missing reactive benefits of Angular Signals
- Potential memory leaks and performance issues
- Complex subscription management

#### Critical Components Analysis

**code-window.component.ts (35+ BehaviorSubjects):**
```typescript
// Current problematic pattern
private showUIDesignOverviewTab$ = new BehaviorSubject<boolean>(false);
private uiDesignPages$ = new BehaviorSubject<MobilePage[]>([]);
private currentUIDesignPageIndex$ = new BehaviorSubject<number>(0);
private isUIDesignLoading$ = new BehaviorSubject<boolean>(false);
private files$ = new BehaviorSubject<FileModel[]>([]);
private isResizing$ = new BehaviorSubject<boolean>(false);
private currentView$ = new BehaviorSubject<ViewType>('preview');
```

**Recommended Signals Pattern:**
```typescript
// Modern Angular 19+ signals approach
export class CodeWindowComponent {
  // State signals
  readonly showUIDesignOverviewTab = signal<boolean>(false);
  readonly uiDesignPages = signal<MobilePage[]>([]);
  readonly currentUIDesignPageIndex = signal<number>(0);
  readonly isUIDesignLoading = signal<boolean>(false);
  readonly files = signal<FileModel[]>([]);
  readonly isResizing = signal<boolean>(false);
  readonly currentView = signal<ViewType>('preview');

  // Computed signals for derived state
  readonly hasUIDesignPages = computed(() => this.uiDesignPages().length > 0);
  readonly currentUIDesignPage = computed(() => {
    const pages = this.uiDesignPages();
    const index = this.currentUIDesignPageIndex();
    return pages[index] || null;
  });
  readonly isPreviewMode = computed(() => this.currentView() === 'preview');
}
```

#### Migration Priority Order
1. **Phase 1**: Simple boolean/primitive state signals
2. **Phase 2**: Array and object state signals
3. **Phase 3**: Complex computed signals and effects
4. **Phase 4**: Integration with existing async pipes

#### Benefits
- Automatic change detection optimization
- Better performance with fine-grained reactivity
- Simplified state management
- Reduced memory footprint
- Type-safe reactive programming

### 3. Subscription Management Modernization
**Impact: HIGH | Effort: MEDIUM | Timeline: 2-3 weeks**

#### Current Issues
- Manual subscription cleanup in `ngOnDestroy`
- Inconsistent use of `takeUntilDestroyed()`
- Potential memory leaks
- Verbose cleanup code

#### Problem Example (code-window.component.ts):
```typescript
// Current problematic pattern
ngOnDestroy() {
  this.clearRegenerationTimeout();
  
  if (this.subscription) {
    this.subscription.unsubscribe();
    this.subscription = null;
  }
  
  if (this.sseDataProcessorSubscription) {
    this.sseDataProcessorSubscription.unsubscribe();
    this.sseDataProcessorSubscription = null;
  }
  
  this.destroy$.next();
  this.destroy$.complete();
  
  // ... 20+ more manual cleanup lines
}
```

#### Recommended Solution:
```typescript
// Use existing SubscriptionManager utility
export class CodeWindowComponent {
  private readonly subscriptionManager = new SubscriptionManager();
  
  ngOnInit() {
    // Automatic cleanup with takeUntilDestroyed
    this.subscriptionManager.subscribe(
      this.enhancedSSEService.sseData$,
      (data) => this.handleSSEData(data)
    );
    
    this.subscriptionManager.subscribe(
      this.stepperStateService.stepperState$,
      (state) => this.handleStepperState(state)
    );
  }
  
  // ngOnDestroy becomes much simpler or can be removed entirely
}
```

### 4. Monaco Editor Bundle Optimization
**Impact: HIGH | Effort: MEDIUM | Timeline: 1-2 weeks**

#### Current Issues
- Aggressive feature exclusion may limit functionality
- Bundle size could be further optimized
- Missing progressive loading opportunities

#### Current Configuration Analysis:
```javascript
// webpack.config.js - Current configuration
new MonacoWebpackPlugin({
  languages: ['typescript', 'javascript', 'css', 'html', 'json'],
  features: [
    '!accessibilityHelp',    // May be needed for accessibility
    '!bracketMatching',      // Useful for code editing
    '!find',                 // Essential feature disabled
    '!folding',              // Code folding is valuable
    // ... 30+ disabled features
  ]
})
```

#### Recommended Optimized Configuration:
```javascript
// Balanced approach - keep essential features
new MonacoWebpackPlugin({
  languages: ['typescript', 'javascript', 'css', 'html', 'json'],
  filename: '[name].worker.js',
  features: [
    // Keep essential features
    'bracketMatching',
    'find',
    'folding',
    'format',
    'hover',
    'links',
    // Disable non-essential features
    '!accessibilityHelp',
    '!colorPicker',
    '!contextmenu',
    '!gotoSymbol',
    '!inspectTokens',
    '!quickCommand',
    '!referenceSearch'
  ],
  // Add progressive loading
  customLanguages: [
    {
      label: 'yaml',
      entry: 'monaco-yaml',
      worker: {
        id: 'monaco-yaml/yamlWorker',
        entry: 'monaco-yaml/yaml.worker'
      }
    }
  ]
})
```

#### Implementation Strategy
1. **Audit Current Usage**: Identify which disabled features are actually needed
2. **Progressive Loading**: Implement lazy loading for advanced features
3. **Bundle Analysis**: Use webpack-bundle-analyzer to measure impact
4. **Performance Testing**: Benchmark before/after changes

### 5. Performance Bottleneck Fixes
**Impact: MEDIUM | Effort: LOW | Timeline: 1 week**

#### High-Frequency Polling Issue:
```typescript
// viewport-debug.component.ts - Current problematic pattern
ngOnInit(): void {
  this.debugInfo$ = interval(500).pipe(  // Too frequent!
    startWith(0),
    map(() => this.viewportService.getViewportDebugInfo()),
    takeUntilDestroyed(this.destroyRef)
  );
}
```

#### Recommended Fix:
```typescript
// Optimized polling with conditional updates
ngOnInit(): void {
  this.debugInfo$ = merge(
    // Slower polling for regular updates
    interval(2000).pipe(
      map(() => this.viewportService.getViewportDebugInfo())
    ),
    // Event-driven updates for immediate feedback
    this.viewportService.viewportChanged$.pipe(
      debounceTime(100),
      map(() => this.viewportService.getViewportDebugInfo())
    )
  ).pipe(
    distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b)),
    takeUntilDestroyed(this.destroyRef)
  );
}
```

## 📊 Implementation Timeline

### Phase 1: Quick Wins (Week 1-2)
- [ ] Add missing trackBy functions to ngFor loops
- [ ] Fix high-frequency polling intervals
- [ ] Optimize Monaco webpack configuration
- [ ] Migrate 5 smallest components to inject()

### Phase 2: Foundation (Week 3-4)
- [ ] Implement SubscriptionManager in major components
- [ ] Add comprehensive TypeScript typing
- [ ] Start BehaviorSubject → Signals migration (simple cases)

### Phase 3: Architecture (Week 5-8)
- [ ] Complete inject() migration across all components
- [ ] Migrate complex state management to signals
- [ ] Implement theme management standardization
- [ ] Eliminate code duplication patterns

## 📈 Expected Impact

### Performance Improvements
- **Bundle Size**: 10-15% reduction through Monaco optimization
- **Runtime Performance**: 20-30% improvement in change detection cycles
- **Memory Usage**: 15-25% reduction through proper subscription cleanup
- **Initial Load Time**: 5-10% improvement through better tree-shaking

### Developer Experience
- **Code Consistency**: 95% adherence to Angular 19+ patterns
- **Maintainability**: 30% reduction in code duplication
- **Type Safety**: Complete elimination of `any` types
- **Testing**: 40% easier component testing with inject() pattern

### Technical Debt Metrics
- **Legacy Pattern Elimination**: 80% migration to modern patterns
- **Subscription Leak Prevention**: 100% automatic cleanup
- **Code Quality Score**: Improved ESLint/TypeScript compliance by 25%

## 🛠️ Tools and Utilities

### Available Utilities
- `SubscriptionManager` - Modern subscription management
- `ThemeManagerMixin` - Centralized theme handling
- `MonacoOptimizationPipe` - Editor performance optimization
- `UIDesignComputationPipe` - Cached computation results

### Recommended Development Tools
- `webpack-bundle-analyzer` - Bundle size analysis
- `Angular DevTools` - Performance profiling
- `ESLint Angular rules` - Code quality enforcement
- `TypeScript strict mode` - Enhanced type checking

## 🔧 Detailed Implementation Examples

### trackBy Function Implementation

#### Missing trackBy in Chat Interface:
```typescript
// chat-interface.component.html - Current
<div *ngFor="let message of messages" class="message-wrapper">

// Recommended fix
<div *ngFor="let message of messages; trackBy: trackByMessage" class="message-wrapper">
```

```typescript
// chat-interface.component.ts - Add trackBy function
trackByMessage(index: number, message: ChatMessage): string {
  return message.id || `${message.from}-${message.timestamp}-${index}`;
}
```

#### Canvas Board Optimization:
```typescript
// canvas-board.component.ts - Optimize node rendering
trackByNodeId(index: number, node: CanvasNode): string {
  return node.id;
}

trackByEdgeId(index: number, edge: CanvasEdge): string {
  return edge.id;
}
```

### Signal-Based State Management Examples

#### Complex State Migration (UI Design Code Preview):
```typescript
// Before: BehaviorSubject pattern
export class UIDesignCodePreviewComponent {
  private isLoading$ = new BehaviorSubject<boolean>(false);
  private hasError$ = new BehaviorSubject<boolean>(false);
  private errorMessage$ = new BehaviorSubject<string>('');
  private backupNodes$ = new BehaviorSubject<UIDesignNode[]>([]);

  // Template usage
  // <div *ngIf="isLoading$ | async">Loading...</div>
}

// After: Signals pattern
export class UIDesignCodePreviewComponent {
  // State signals
  readonly isLoading = signal<boolean>(false);
  readonly hasError = signal<boolean>(false);
  readonly errorMessage = signal<string>('');
  readonly backupNodes = signal<UIDesignNode[]>([]);

  // Computed signals for complex logic
  readonly canShowContent = computed(() =>
    !this.isLoading() && !this.hasError() && this.backupNodes().length > 0
  );

  readonly statusMessage = computed(() => {
    if (this.isLoading()) return 'Loading UI design...';
    if (this.hasError()) return this.errorMessage();
    return `Loaded ${this.backupNodes().length} design nodes`;
  });

  // Template usage becomes simpler
  // <div *ngIf="isLoading()">Loading...</div>
  // <div *ngIf="canShowContent()">{{ statusMessage() }}</div>
}
```

### Advanced Subscription Management

#### Service Integration Pattern:
```typescript
// Enhanced service with automatic cleanup
export class CodeWindowService {
  private readonly subscriptionManager = new SubscriptionManager();
  private readonly destroyRef = inject(DestroyRef);

  // State signals
  readonly files = signal<FileModel[]>([]);
  readonly currentView = signal<ViewType>('preview');

  constructor() {
    this.initializeSubscriptions();
  }

  private initializeSubscriptions(): void {
    // SSE data subscription
    this.subscriptionManager.subscribe(
      this.enhancedSSEService.sseData$,
      (data) => this.handleSSEData(data),
      (error) => this.handleSSEError(error)
    );

    // File changes subscription
    this.subscriptionManager.subscribe(
      this.fileContentCache.contentChanges$,
      (changes) => this.handleFileChanges(changes)
    );

    // Theme changes subscription
    this.subscriptionManager.subscribe(
      this.themeService.themeObservable,
      (theme) => this.handleThemeChange(theme)
    );
  }

  private handleSSEData(data: SSEData): void {
    // Update signals instead of BehaviorSubjects
    if (data.type === 'files') {
      this.files.set(data.files);
    }
  }
}
```

## 🧪 Testing Strategies

### Unit Testing Modernized Components

#### Testing inject() Pattern:
```typescript
// component.spec.ts - Modern testing approach
describe('ChatWindowComponent', () => {
  let component: ChatWindowComponent;
  let fixture: ComponentFixture<ChatWindowComponent>;
  let mockStepperService: jasmine.SpyObj<StepperStateService>;

  beforeEach(() => {
    const stepperSpy = jasmine.createSpyObj('StepperStateService', ['getState']);

    TestBed.configureTestingModule({
      imports: [ChatWindowComponent],
      providers: [
        { provide: StepperStateService, useValue: stepperSpy }
      ]
    });

    fixture = TestBed.createComponent(ChatWindowComponent);
    component = fixture.componentInstance;
    mockStepperService = TestBed.inject(StepperStateService) as jasmine.SpyObj<StepperStateService>;
  });

  it('should handle stepper state changes', () => {
    const mockState = { currentStep: 1, isComplete: false };
    mockStepperService.getState.and.returnValue(mockState);

    component.ngOnInit();

    expect(component.stepperState()).toEqual(mockState);
  });
});
```

#### Testing Signals:
```typescript
// Testing signal-based components
describe('UIDesignCodePreviewComponent', () => {
  it('should compute canShowContent correctly', () => {
    component.isLoading.set(false);
    component.hasError.set(false);
    component.backupNodes.set([mockNode1, mockNode2]);

    expect(component.canShowContent()).toBe(true);

    component.isLoading.set(true);
    expect(component.canShowContent()).toBe(false);
  });

  it('should update status message reactively', () => {
    component.isLoading.set(true);
    expect(component.statusMessage()).toBe('Loading UI design...');

    component.isLoading.set(false);
    component.backupNodes.set([mockNode1, mockNode2]);
    expect(component.statusMessage()).toBe('Loaded 2 design nodes');
  });
});
```

### Performance Testing

#### Bundle Size Monitoring:
```bash
# Add to package.json scripts
"analyze:bundle": "ng build --stats-json && npx webpack-bundle-analyzer dist/experience-studio/stats.json",
"test:performance": "ng build --configuration=production && npm run analyze:bundle"
```

#### Memory Leak Detection:
```typescript
// performance.spec.ts - Memory leak testing
describe('Memory Management', () => {
  it('should not leak subscriptions', fakeAsync(() => {
    const component = TestBed.createComponent(CodeWindowComponent).componentInstance;

    // Simulate component lifecycle
    component.ngOnInit();
    tick(1000);

    // Check subscription count before destroy
    const initialSubscriptions = getActiveSubscriptionCount();

    component.ngOnDestroy();
    tick(100);

    // Verify subscriptions are cleaned up
    const finalSubscriptions = getActiveSubscriptionCount();
    expect(finalSubscriptions).toBeLessThanOrEqual(initialSubscriptions);
  }));
});
```

## 📊 Monitoring and Metrics

### Performance Monitoring Setup

#### Bundle Size Tracking:
```json
// angular.json - Add budget monitoring
"budgets": [
  {
    "type": "initial",
    "maximumWarning": "450kB",  // Reduced from 500kB
    "maximumError": "900kB"     // Reduced from 1MB
  },
  {
    "type": "anyComponentStyle",
    "maximumWarning": "8kB",
    "maximumError": "15kB"
  }
]
```

#### Runtime Performance Metrics:
```typescript
// performance-monitor.service.ts
@Injectable({ providedIn: 'root' })
export class PerformanceMonitorService {
  private readonly metrics = signal<PerformanceMetrics>({
    changeDetectionCycles: 0,
    componentRenderTime: 0,
    memoryUsage: 0,
    bundleLoadTime: 0
  });

  measureChangeDetection(): void {
    const start = performance.now();
    // Trigger change detection
    const end = performance.now();

    this.metrics.update(current => ({
      ...current,
      changeDetectionCycles: current.changeDetectionCycles + 1,
      componentRenderTime: end - start
    }));
  }

  getMetrics(): PerformanceMetrics {
    return this.metrics();
  }
}
```

### Code Quality Metrics

#### ESLint Configuration:
```json
// .eslintrc.json - Enhanced rules for optimization
{
  "extends": ["@angular-eslint/recommended"],
  "rules": {
    "@angular-eslint/prefer-on-push-component-change-detection": "error",
    "@angular-eslint/use-injectable-provided-in": "error",
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/prefer-readonly": "error",
    "rxjs/no-unsafe-takeuntil": "error",
    "rxjs/prefer-takeuntil": "error"
  }
}
```

## 🚀 Migration Checklist

### Phase 1: Foundation (Weeks 1-2)
- [ ] **Monaco Optimization**
  - [ ] Audit current feature usage
  - [ ] Update webpack configuration
  - [ ] Test bundle size reduction
  - [ ] Verify functionality preservation

- [ ] **trackBy Functions**
  - [ ] Identify all ngFor loops without trackBy
  - [ ] Implement trackBy functions
  - [ ] Test performance improvement
  - [ ] Document trackBy patterns

- [ ] **Polling Optimization**
  - [ ] Audit all interval/timer usage
  - [ ] Implement event-driven alternatives
  - [ ] Add conditional polling logic
  - [ ] Monitor CPU usage improvement

### Phase 2: Modernization (Weeks 3-4)
- [ ] **inject() Migration**
  - [ ] Create migration script/tool
  - [ ] Migrate small components first
  - [ ] Update unit tests
  - [ ] Verify dependency injection works

- [ ] **Subscription Management**
  - [ ] Implement SubscriptionManager usage
  - [ ] Remove manual ngOnDestroy cleanup
  - [ ] Add memory leak tests
  - [ ] Monitor subscription cleanup

### Phase 3: Architecture (Weeks 5-8)
- [ ] **Signals Migration**
  - [ ] Start with simple state signals
  - [ ] Migrate computed properties
  - [ ] Update templates to use signals
  - [ ] Test reactive behavior

- [ ] **Code Consolidation**
  - [ ] Apply ThemeManagerMixin
  - [ ] Extract common patterns
  - [ ] Remove duplicate code
  - [ ] Update documentation

## 📋 Success Criteria

### Performance Benchmarks
- [ ] Bundle size reduced by 10-15%
- [ ] Initial load time improved by 5-10%
- [ ] Change detection cycles reduced by 20-30%
- [ ] Memory usage decreased by 15-25%

### Code Quality Metrics
- [ ] Zero `any` types in production code
- [ ] 95% components using OnPush change detection
- [ ] 100% ngFor loops have trackBy functions
- [ ] All subscriptions use automatic cleanup

### Developer Experience
- [ ] Consistent inject() pattern across codebase
- [ ] Standardized state management with signals
- [ ] Comprehensive TypeScript typing
- [ ] Improved test coverage and reliability

---

*This document serves as the definitive guide for Experience Studio optimization. Update progress in the checklist sections as work is completed.*
